#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大乐透历史数据获取工具
从官方API获取大乐透历史开奖数据并保存为CSV格式
"""

import requests
import pandas as pd
import json
import time
import os

class LotteryDataFetcher:
    def __init__(self):
        self.api_url = "https://webapi.sporttery.cn/gateway/lottery/getHistoryPageListV1.qry"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://www.sporttery.cn/',
        }
        self.csv_filename = "lottery_history.csv"
    
    def fetch_lottery_data(self, page_size=200, page_no=1):
        """
        从API获取大乐透历史数据
        
        Args:
            page_size: 每页数据量，默认200
            page_no: 页码，默认1
            
        Returns:
            dict: API返回的数据
        """
        params = {
            'gameNo': '85',  # 大乐透游戏编号
            'provinceId': '0',
            'pageSize': str(page_size),
            'isVerify': '1',
            'pageNo': str(page_no)
        }
        
        try:
            print(f"正在获取第{page_no}页数据，每页{page_size}条...")
            response = requests.get(self.api_url, params=params, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if data.get('success') and data.get('errorCode') == '0':
                return data
            else:
                print(f"API返回错误: {data.get('errorMessage', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return None
    
    def parse_lottery_data(self, api_data):
        """
        解析API数据，提取需要的字段
        
        Args:
            api_data: API返回的原始数据
            
        Returns:
            list: 解析后的数据列表
        """
        if not api_data or 'value' not in api_data or 'list' not in api_data['value']:
            return []
        
        lottery_list = []
        for item in api_data['value']['list']:
            try:
                # 提取基本信息
                draw_num = item.get('lotteryDrawNum', '')
                draw_time = item.get('lotteryDrawTime', '')
                draw_result = item.get('lotteryDrawResult', '')
                
                # 数据验证
                if not all([draw_num, draw_time, draw_result]):
                    continue

                # 验证号码格式（应该是7个数字，空格分隔）
                if len(draw_result.split()) != 7:
                    continue
                
                lottery_list.append({
                    '期号': draw_num,
                    '开奖时间': draw_time,
                    '中奖号码': draw_result
                })
                
            except Exception:
                continue
        
        return lottery_list
    
    def save_to_csv(self, lottery_data, filename=None):
        """
        将数据保存到CSV文件
        
        Args:
            lottery_data: 彩票数据列表
            filename: 文件名，默认使用类属性
        """
        if not lottery_data:
            print("没有数据需要保存")
            return False
        
        filename = filename or self.csv_filename
        
        try:
            # 创建DataFrame
            df = pd.DataFrame(lottery_data)
            
            # 按期号排序（降序，最新的在前面）
            df = df.sort_values('期号', ascending=False)
            
            # 保存到CSV
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"数据已保存到 {filename}")
            print(f"共保存 {len(df)} 条记录")
            
            # 显示前几条数据作为预览
            print("\n数据预览:")
            print(df.head().to_string(index=False))
            
            return True
            
        except Exception as e:
            print(f"保存CSV文件时出错: {e}")
            return False
    
    def get_lottery_history(self, periods=200):
        """
        获取指定期数的大乐透历史数据

        Args:
            periods: 要获取的期数，默认200期
        """
        print(f"开始获取大乐透历史数据，目标期数: {periods}")
        print("=" * 50)

        all_lottery_data = []
        page_no = 1
        page_size = 100  # 每页100条，确保稳定获取

        while len(all_lottery_data) < periods:
            # 计算本次需要获取的数量
            remaining = periods - len(all_lottery_data)
            current_page_size = min(page_size, remaining)

            # 获取数据
            api_data = self.fetch_lottery_data(page_size=current_page_size, page_no=page_no)

            if not api_data:
                print(f"第{page_no}页数据获取失败")
                break

            # 解析数据
            lottery_data = self.parse_lottery_data(api_data)

            if not lottery_data:
                print(f"第{page_no}页数据解析失败或无数据")
                break

            all_lottery_data.extend(lottery_data)
            print(f"第{page_no}页获取到 {len(lottery_data)} 条数据，累计 {len(all_lottery_data)} 条")

            # 如果本页数据少于请求数量，说明已经到底了
            if len(lottery_data) < current_page_size:
                print("已获取所有可用数据")
                break

            page_no += 1
            time.sleep(0.5)  # 避免请求过快

        if not all_lottery_data:
            print("解析数据失败或无有效数据")
            return False

        print(f"总共成功获取 {len(all_lottery_data)} 条数据")

        # 去重处理（按期号）
        seen_periods = set()
        unique_data = []
        for item in all_lottery_data:
            if item['期号'] not in seen_periods:
                unique_data.append(item)
                seen_periods.add(item['期号'])

        if len(unique_data) != len(all_lottery_data):
            print(f"去重后剩余 {len(unique_data)} 条数据")

        # 保存数据
        success = self.save_to_csv(unique_data)

        if success:
            print("=" * 50)
            print("数据获取完成！")
            print(f"文件位置: {os.path.abspath(self.csv_filename)}")

            # 统计信息
            if unique_data:
                latest_period = max(unique_data, key=lambda x: x['期号'])
                oldest_period = min(unique_data, key=lambda x: x['期号'])
                print(f"最新期号: {latest_period['期号']} ({latest_period['开奖时间']})")
                print(f"最早期号: {oldest_period['期号']} ({oldest_period['开奖时间']})")

        return success

def main():
    """主函数"""
    print("大乐透历史数据获取工具")
    print("=" * 50)
    
    # 创建获取器实例
    fetcher = LotteryDataFetcher()
    
    # 获取200期历史数据
    success = fetcher.get_lottery_history(periods=200)
    
    if success:
        print("\n✅ 任务完成！")
    else:
        print("\n❌ 任务失败！")

if __name__ == "__main__":
    main()
