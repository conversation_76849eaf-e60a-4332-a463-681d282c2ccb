#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大乐透基础分析库
基于真实中奖记录进行统计分析，确保数据的唯一性和准确性
"""

import pandas as pd
import numpy as np
from collections import Counter, defaultdict
from datetime import datetime
import os

class LotteryAnalyzer:
    """大乐透分析器 - 基于真实中奖记录的统计分析"""
    
    def __init__(self, csv_file="lottery_history.csv"):
        """
        初始化分析器
        
        Args:
            csv_file: CSV数据文件路径
        """
        self.csv_file = csv_file
        self.data = None
        self.red_balls = []  # 红球数据 (1-35)
        self.blue_balls = []  # 蓝球数据 (1-12)
        self.periods = []  # 期号列表
        self.dates = []  # 开奖日期列表
        
    def load_data(self):
        """
        加载CSV数据并进行预处理
        
        Returns:
            bool: 加载成功返回True，失败返回False
        """
        try:
            if not os.path.exists(self.csv_file):
                print(f"错误：文件 {self.csv_file} 不存在")
                return False
            
            # 读取CSV文件
            self.data = pd.read_csv(self.csv_file, encoding='utf-8-sig')
            
            # 验证数据结构
            required_columns = ['期号', '开奖时间', '中奖号码']
            if not all(col in self.data.columns for col in required_columns):
                print(f"错误：CSV文件缺少必要列：{required_columns}")
                return False
            
            # 清理和验证数据
            self._preprocess_data()
            
            print(f"成功加载 {len(self.data)} 期真实中奖记录")
            print(f"数据范围：{self.periods[-1]} 到 {self.periods[0]}")
            
            return True
            
        except Exception as e:
            print(f"加载数据时出错：{e}")
            return False
    
    def _preprocess_data(self):
        """数据预处理：分离红球蓝球，验证数据完整性"""
        
        self.red_balls = []
        self.blue_balls = []
        self.periods = []
        self.dates = []

        # 缓存频繁使用的计算结果以提高性能
        self._red_counter_cache = None
        self._blue_counter_cache = None
        self._red_concat_cache = None
        self._blue_concat_cache = None
        
        # 使用向量化操作替代iterrows()以提高性能
        valid_mask = pd.Series([True] * len(self.data), index=self.data.index)

        # 预处理中奖号码列，转换为字符串并去除空白
        numbers_series = self.data['中奖号码'].astype(str).str.strip()

        # 分割号码并验证格式
        split_numbers = numbers_series.str.split()

        # 验证号码数量
        length_mask = split_numbers.str.len() == 7
        invalid_length = ~length_mask
        if invalid_length.any():
            invalid_periods = self.data.loc[invalid_length, '期号'].astype(str)
            for period in invalid_periods:
                print(f"警告：期号 {period} 号码格式错误，跳过")
        valid_mask &= length_mask

        # 转换为数值并验证
        valid_data = self.data[valid_mask].copy()
        valid_split = split_numbers[valid_mask]

        for idx in valid_data.index:
            try:
                period = str(valid_data.loc[idx, '期号'])
                date = valid_data.loc[idx, '开奖时间']
                number_list = valid_split.loc[idx]

                # 转换为整数
                try:
                    nums = [int(n) for n in number_list]
                except ValueError:
                    print(f"警告：期号 {period} 包含非数字，跳过")
                    valid_mask.loc[idx] = False
                    continue

                # 分离红球和蓝球
                red_nums = nums[:5]  # 前5个是红球
                blue_nums = nums[5:7]  # 后2个是蓝球

                # 验证红球范围 (1-35)
                if not all(1 <= n <= 35 for n in red_nums):
                    print(f"警告：期号 {period} 红球超出范围(1-35)，跳过")
                    valid_mask.loc[idx] = False
                    continue

                # 验证蓝球范围 (1-12)
                if not all(1 <= n <= 12 for n in blue_nums):
                    print(f"警告：期号 {period} 蓝球超出范围(1-12)，跳过")
                    valid_mask.loc[idx] = False
                    continue

                # 验证红球无重复
                if len(set(red_nums)) != 5:
                    print(f"警告：期号 {period} 红球有重复，跳过")
                    valid_mask.loc[idx] = False
                    continue

                # 验证蓝球无重复
                if len(set(blue_nums)) != 2:
                    print(f"警告：期号 {period} 蓝球有重复，跳过")
                    valid_mask.loc[idx] = False
                    continue

                # 数据有效，添加到列表
                self.red_balls.append(red_nums)
                self.blue_balls.append(blue_nums)
                self.periods.append(period)
                self.dates.append(date)

            except Exception as e:
                print(f"处理期号 {valid_data.loc[idx, '期号']} 时出错：{e}")
                valid_mask.loc[idx] = False
                continue
        
        # 检查并处理重复期号
        if len(self.periods) > 1:
            seen_periods = set()
            unique_indices = []
            for i, period in enumerate(self.periods):
                if period not in seen_periods:
                    seen_periods.add(period)
                    unique_indices.append(i)
                else:
                    print(f"警告：发现重复期号 {period}，保留第一个记录")

            # 如果有重复，只保留唯一的记录
            if len(unique_indices) < len(self.periods):
                self.periods = [self.periods[i] for i in unique_indices]
                self.dates = [self.dates[i] for i in unique_indices]
                self.red_balls = [self.red_balls[i] for i in unique_indices]
                self.blue_balls = [self.blue_balls[i] for i in unique_indices]

        # 确保数据按期号降序排列（最新期在前）- 优化内存使用
        if len(self.periods) > 1:
            # 使用numpy数组进行高效排序
            periods_array = np.array([int(p) for p in self.periods], dtype=np.int32)
            sort_indices = np.argsort(periods_array)[::-1]  # 降序排序

            # 重新排列所有数据
            self.periods = [self.periods[i] for i in sort_indices]
            self.dates = [self.dates[i] for i in sort_indices]
            self.red_balls = [self.red_balls[i] for i in sort_indices]
            self.blue_balls = [self.blue_balls[i] for i in sort_indices]

        # 清理缓存，因为数据已更新
        self._clear_cache()
        print(f"数据预处理完成，有效记录：{len(self.red_balls)} 期")

    def _clear_cache(self):
        """清理缓存的计算结果"""
        self._red_counter_cache = None
        self._blue_counter_cache = None
        self._red_concat_cache = None
        self._blue_concat_cache = None

    def _get_red_numbers_array(self):
        """获取缓存的红球数组"""
        if self._red_concat_cache is None:
            self._red_concat_cache = np.concatenate(self.red_balls, dtype=np.int8)
        return self._red_concat_cache

    def _get_blue_numbers_array(self):
        """获取缓存的蓝球数组"""
        if self._blue_concat_cache is None:
            self._blue_concat_cache = np.concatenate(self.blue_balls, dtype=np.int8)
        return self._blue_concat_cache

    def _get_red_counter(self):
        """获取缓存的红球计数器"""
        if self._red_counter_cache is None:
            self._red_counter_cache = Counter(self._get_red_numbers_array())
        return self._red_counter_cache

    def _get_blue_counter(self):
        """获取缓存的蓝球计数器"""
        if self._blue_counter_cache is None:
            self._blue_counter_cache = Counter(self._get_blue_numbers_array())
        return self._blue_counter_cache
    
    def get_data_summary(self):
        """
        获取数据概览
        
        Returns:
            dict: 数据统计信息
        """
        if self.data is None:
            return {"error": "数据未加载，请先调用 load_data()"}
        
        return {
            "总期数": len(self.periods),
            "最新期号": self.periods[0] if self.periods else None,
            "最早期号": self.periods[-1] if self.periods else None,
            "最新开奖日期": self.dates[0] if self.dates else None,
            "最早开奖日期": self.dates[-1] if self.dates else None,
            "红球范围": "1-35",
            "蓝球范围": "1-12",
            "数据完整性": "已验证"
        }
    
    def analyze_frequency(self, ball_type="red"):
        """
        分析号码出现频率
        
        Args:
            ball_type: "red" 或 "blue"
            
        Returns:
            dict: 频率统计结果
        """
        if not self.red_balls or len(self.red_balls) == 0:
            return {"error": "数据未加载或为空，请先调用 load_data()"}

        # 参数验证
        if ball_type not in ["red", "blue"]:
            return {"error": "ball_type参数必须是'red'或'blue'"}

        if ball_type == "red":
            # 使用缓存的计数器提高性能
            counter = self._get_red_counter()
            total_draws = len(self.red_balls)

            # 添加除零保护
            if total_draws == 0:
                return {"error": "数据为空，无法进行频率分析"}

            # 计算频率和期望频率
            expected_freq = total_draws * 5 / 35  # 每期5个红球，共35个号码

            # 准备卡方检验数据
            observed_frequencies = [counter.get(num, 0) for num in range(1, 36)]
            expected_frequencies = [expected_freq] * 35

            # 计算卡方统计量和p值
            chi2_stat, p_value = self._chi_square_test(observed_frequencies, expected_frequencies)

            result = {
                "球类型": "红球",
                "统计期数": total_draws,
                "号码范围": "1-35",
                "期望频率": round(expected_freq, 2),
                "统计检验": {
                    "卡方统计量": round(chi2_stat, 4),
                    "p值": round(p_value, 6),
                    "显著性": "显著" if p_value < 0.05 else "不显著",
                    "自由度": 34
                },
                "频率统计": {}
            }

            for num in range(1, 36):
                actual_freq = counter.get(num, 0)
                result["频率统计"][num] = {
                    "出现次数": actual_freq,
                    "出现频率": round(actual_freq / total_draws, 4),
                    "偏差": round(actual_freq - expected_freq, 2),
                    "标准化偏差": round((actual_freq - expected_freq) / np.sqrt(expected_freq), 2)
                }
        
        elif ball_type == "blue":
            # 使用缓存的计数器提高性能
            counter = self._get_blue_counter()
            total_draws = len(self.blue_balls)

            # 添加除零保护
            if total_draws == 0:
                return {"error": "数据为空，无法进行频率分析"}

            # 计算频率和期望频率
            expected_freq = total_draws * 2 / 12  # 每期2个蓝球，共12个号码

            # 准备卡方检验数据
            observed_frequencies = [counter.get(num, 0) for num in range(1, 13)]
            expected_frequencies = [expected_freq] * 12

            # 计算卡方统计量和p值
            chi2_stat, p_value = self._chi_square_test(observed_frequencies, expected_frequencies)

            result = {
                "球类型": "蓝球",
                "统计期数": total_draws,
                "号码范围": "1-12",
                "期望频率": round(expected_freq, 2),
                "统计检验": {
                    "卡方统计量": round(chi2_stat, 4),
                    "p值": round(p_value, 6),
                    "显著性": "显著" if p_value < 0.05 else "不显著",
                    "自由度": 11
                },
                "频率统计": {}
            }

            for num in range(1, 13):
                actual_freq = counter.get(num, 0)
                result["频率统计"][num] = {
                    "出现次数": actual_freq,
                    "出现频率": round(actual_freq / total_draws, 4),
                    "偏差": round(actual_freq - expected_freq, 2),
                    "标准化偏差": round((actual_freq - expected_freq) / np.sqrt(expected_freq), 2)
                }
        
        else:
            return {"error": "ball_type 必须是 'red' 或 'blue'"}
        
        return result

    def analyze_hot_cold(self, ball_type="red", recent_periods=50):
        """
        分析冷热号码（基于最近N期）

        Args:
            ball_type: "red" 或 "blue"
            recent_periods: 分析最近多少期，默认50期

        Returns:
            dict: 冷热号分析结果
        """
        if not self.red_balls or len(self.red_balls) == 0:
            return {"error": "数据未加载或为空，请先调用 load_data()"}

        # 参数验证
        if ball_type not in ["red", "blue"]:
            return {"error": "ball_type参数必须是'red'或'blue'"}

        if not isinstance(recent_periods, int) or recent_periods <= 0:
            return {"error": "recent_periods参数必须是正整数"}

        # 确保不超过总期数
        actual_periods = min(recent_periods, len(self.red_balls))

        if ball_type == "red":
            # 分析最近N期的红球 - 优化性能
            recent_data = self.red_balls[:actual_periods]
            all_numbers = np.concatenate(recent_data, dtype=np.int8)
            counter = Counter(all_numbers)
            number_range = range(1, 36)
            expected_freq = actual_periods * 5 / 35

        elif ball_type == "blue":
            # 分析最近N期的蓝球 - 优化性能
            recent_data = self.blue_balls[:actual_periods]
            all_numbers = np.concatenate(recent_data, dtype=np.int8)
            counter = Counter(all_numbers)
            number_range = range(1, 13)
            expected_freq = actual_periods * 2 / 12

        else:
            return {"error": "ball_type 必须是 'red' 或 'blue'"}

        # 计算冷热号 - 使用统计学方法分类
        frequencies = [(num, counter.get(num, 0)) for num in number_range]
        frequencies.sort(key=lambda x: x[1], reverse=True)

        # 基于标准差的科学分类方法
        total_numbers = len(frequencies)
        if total_numbers == 0:
            hot_numbers = warm_numbers = cold_numbers = []
        else:
            # 计算频率的统计特征
            freq_values = [freq for _, freq in frequencies]
            mean_freq = np.mean(freq_values)
            std_freq = np.std(freq_values)

            # 基于标准差定义冷热号阈值
            # 热号：频率 > 均值 + 0.5*标准差
            # 冷号：频率 < 均值 - 0.5*标准差
            # 温号：介于两者之间
            hot_threshold = mean_freq + 0.5 * std_freq
            cold_threshold = mean_freq - 0.5 * std_freq

            hot_numbers = [(num, freq) for num, freq in frequencies if freq > hot_threshold]
            cold_numbers = [(num, freq) for num, freq in frequencies if freq < cold_threshold]
            warm_numbers = [(num, freq) for num, freq in frequencies
                          if cold_threshold <= freq <= hot_threshold]

        return {
            "球类型": "红球" if ball_type == "red" else "蓝球",
            "分析期数": actual_periods,
            "期望频率": round(expected_freq, 2),
            "统计信息": {
                "平均频率": round(mean_freq, 2) if total_numbers > 0 else 0,
                "频率标准差": round(std_freq, 2) if total_numbers > 0 else 0,
                "热号阈值": round(hot_threshold, 2) if total_numbers > 0 else 0,
                "冷号阈值": round(cold_threshold, 2) if total_numbers > 0 else 0
            },
            "热号": [{"号码": num, "出现次数": freq, "偏差": round(freq - expected_freq, 2)}
                    for num, freq in hot_numbers],
            "温号": [{"号码": num, "出现次数": freq, "偏差": round(freq - expected_freq, 2)}
                    for num, freq in warm_numbers],
            "冷号": [{"号码": num, "出现次数": freq, "偏差": round(freq - expected_freq, 2)}
                    for num, freq in cold_numbers]
        }

    def analyze_missing(self, ball_type="red"):
        """
        分析号码遗漏情况（从最新期开始计算）

        Args:
            ball_type: "red" 或 "blue"

        Returns:
            dict: 遗漏分析结果
        """
        if not self.red_balls or len(self.red_balls) == 0:
            return {"error": "数据未加载或为空，请先调用 load_data()"}

        # 参数验证
        if ball_type not in ["red", "blue"]:
            return {"error": "ball_type参数必须是'red'或'blue'"}

        if ball_type == "red":
            data = self.red_balls
            number_range = range(1, 36)
        else:  # ball_type == "blue"
            data = self.blue_balls
            number_range = range(1, 13)

        # 优化遗漏分析算法 - O(n)复杂度，只遍历一次数据
        current_missing = {num: len(data) for num in number_range}  # 当前遗漏期数
        historical_max_missing = {num: 0 for num in number_range}  # 历史最大遗漏
        all_missing_records = {num: [] for num in number_range}  # 所有遗漏记录
        last_occurrence = {num: -1 for num in number_range}  # 每个号码的最后出现位置

        # 单次遍历计算所有遗漏信息
        for i, draw in enumerate(data):
            # 将当前期的号码转换为集合以提高查找效率
            draw_set = set(draw)

            for num in number_range:
                if num in draw_set:
                    # 号码出现，更新当前遗漏
                    if current_missing[num] == len(data):  # 首次出现
                        current_missing[num] = i

                    # 计算这次遗漏期数
                    missing_periods = i - last_occurrence[num] - 1
                    if missing_periods > 0:
                        all_missing_records[num].append(missing_periods)
                        historical_max_missing[num] = max(historical_max_missing[num], missing_periods)

                    last_occurrence[num] = i

        # 处理最后一次出现到数据末尾的遗漏
        for num in number_range:
            if last_occurrence[num] >= 0:
                final_missing = len(data) - 1 - last_occurrence[num]
                if final_missing > 0:
                    all_missing_records[num].append(final_missing)
                    historical_max_missing[num] = max(historical_max_missing[num], final_missing)

        # 计算遗漏统计信息
        missing_values = list(current_missing.values())
        historical_max_values = list(historical_max_missing.values())

        # 按当前遗漏期数排序
        sorted_missing = sorted(current_missing.items(), key=lambda x: x[1], reverse=True)

        return {
            "球类型": "红球" if ball_type == "red" else "蓝球",
            "统计期数": len(data),
            "当前遗漏统计": [{"号码": num, "当前遗漏": periods, "历史最大遗漏": historical_max_missing[num]}
                          for num, periods in sorted_missing],
            "遗漏分布统计": {
                "当前最大遗漏": max(missing_values) if missing_values else 0,
                "当前平均遗漏": round(np.mean(missing_values), 2) if missing_values else 0,
                "当前遗漏标准差": round(np.std(missing_values), 2) if missing_values else 0,
                "历史最大遗漏": max(historical_max_values) if historical_max_values else 0,
                "历史平均最大遗漏": round(np.mean(historical_max_values), 2) if historical_max_values else 0
            }
        }

    def analyze_combination_patterns(self):
        """
        分析号码组合模式

        Returns:
            dict: 组合模式分析结果
        """
        if not self.red_balls or len(self.red_balls) == 0:
            return {"error": "数据未加载或为空，请先调用 load_data()"}

        total_draws = len(self.red_balls)

        # 分析红球组合模式
        red_patterns = {
            "奇偶比": defaultdict(int),
            "大小比": defaultdict(int),
            "和值分布": [],
            "连号情况": defaultdict(int)
        }

        for draw in self.red_balls:
            # 奇偶分析
            odd_count = sum(1 for n in draw if n % 2 == 1)
            even_count = 5 - odd_count
            red_patterns["奇偶比"][f"{odd_count}:{even_count}"] += 1

            # 大小分析（1-17为小，18-35为大）
            small_count = sum(1 for n in draw if n <= 17)
            big_count = 5 - small_count
            red_patterns["大小比"][f"{small_count}:{big_count}"] += 1

            # 和值分析
            sum_value = sum(draw)
            red_patterns["和值分布"].append(sum_value)

            # 连号分析 - 统计连号组的个数（优化算法）
            sorted_draw = sorted(draw)
            consecutive_groups = 0
            i = 0
            while i < len(sorted_draw) - 1:
                if sorted_draw[i+1] - sorted_draw[i] == 1:
                    # 找到连号组的开始
                    group_length = 2  # 至少2个连续数字
                    j = i + 1
                    # 继续查找连号组的长度
                    while j < len(sorted_draw) - 1 and sorted_draw[j+1] - sorted_draw[j] == 1:
                        group_length += 1
                        j += 1
                    consecutive_groups += 1
                    i = j  # 跳过已处理的连号组
                else:
                    i += 1
            red_patterns["连号情况"][consecutive_groups] += 1

        # 分析蓝球组合模式
        blue_patterns = {
            "奇偶比": defaultdict(int),
            "大小比": defaultdict(int),
            "和值分布": [],
            "连号情况": defaultdict(int)
        }

        for draw in self.blue_balls:
            # 奇偶分析
            odd_count = sum(1 for n in draw if n % 2 == 1)
            even_count = 2 - odd_count
            blue_patterns["奇偶比"][f"{odd_count}:{even_count}"] += 1

            # 大小分析（1-6为小，7-12为大）
            small_count = sum(1 for n in draw if n <= 6)
            big_count = 2 - small_count
            blue_patterns["大小比"][f"{small_count}:{big_count}"] += 1

            # 和值分析
            sum_value = sum(draw)
            blue_patterns["和值分布"].append(sum_value)

            # 连号分析 - 蓝球只有2个，判断是否连号
            sorted_blue = sorted(draw)
            if sorted_blue[1] - sorted_blue[0] == 1:
                blue_patterns["连号情况"][1] += 1  # 有1个连号组
            else:
                blue_patterns["连号情况"][0] += 1  # 无连号组

        # 计算红球和值的完整统计信息 - 指定数据类型优化性能
        red_sum_values = np.array(red_patterns["和值分布"], dtype=np.int16)
        blue_sum_values = np.array(blue_patterns["和值分布"], dtype=np.int16)

        return {
            "统计期数": total_draws,
            "红球模式": {
                "奇偶比分布": dict(red_patterns["奇偶比"]),
                "大小比分布": dict(red_patterns["大小比"]),
                "和值统计": {
                    "最小值": int(np.min(red_sum_values)) if len(red_sum_values) > 0 else 0,
                    "最大值": int(np.max(red_sum_values)) if len(red_sum_values) > 0 else 0,
                    "平均值": round(float(np.mean(red_sum_values)), 2) if len(red_sum_values) > 0 else 0,
                    "中位数": round(float(np.median(red_sum_values)), 2) if len(red_sum_values) > 0 else 0,
                    "标准差": round(float(np.std(red_sum_values)), 2) if len(red_sum_values) > 0 else 0,
                    "偏度": round(float(self._calculate_skewness(red_sum_values)), 3) if len(red_sum_values) > 0 else 0,
                    "峰度": round(float(self._calculate_kurtosis(red_sum_values)), 3) if len(red_sum_values) > 0 else 0,
                    "理论期望": 90,  # 红球和值理论期望 (1+35)*5/2 = 90
                    "理论标准差": round(float(np.sqrt(5 * (35*36/12) * (35-5)/(35-1))), 2)  # 无重复抽样方差公式
                },
                "连号分布": dict(red_patterns["连号情况"])
            },
            "蓝球模式": {
                "奇偶比分布": dict(blue_patterns["奇偶比"]),
                "大小比分布": dict(blue_patterns["大小比"]),
                "和值统计": {
                    "最小值": int(np.min(blue_sum_values)) if len(blue_sum_values) > 0 else 0,
                    "最大值": int(np.max(blue_sum_values)) if len(blue_sum_values) > 0 else 0,
                    "平均值": round(float(np.mean(blue_sum_values)), 2) if len(blue_sum_values) > 0 else 0,
                    "中位数": round(float(np.median(blue_sum_values)), 2) if len(blue_sum_values) > 0 else 0,
                    "标准差": round(float(np.std(blue_sum_values)), 2) if len(blue_sum_values) > 0 else 0,
                    "偏度": round(float(self._calculate_skewness(blue_sum_values)), 3) if len(blue_sum_values) > 0 else 0,
                    "峰度": round(float(self._calculate_kurtosis(blue_sum_values)), 3) if len(blue_sum_values) > 0 else 0,
                    "理论期望": 13,  # 蓝球和值理论期望 (1+12)*2/2 = 13
                    "理论标准差": round(float(np.sqrt(2 * (12*13/12) * (12-2)/(12-1))), 2)  # 无重复抽样方差公式
                },
                "连号分布": dict(blue_patterns["连号情况"])
            }
        }

    def query_period_data(self, period):
        """
        查询指定期号的中奖数据

        Args:
            period: 期号（字符串或整数）

        Returns:
            dict: 该期的详细信息
        """
        if not self.periods:
            return {"error": "数据未加载，请先调用 load_data()"}

        period_str = str(period)

        try:
            index = self.periods.index(period_str)
            return {
                "期号": period_str,
                "开奖日期": self.dates[index],
                "红球": self.red_balls[index],
                "蓝球": self.blue_balls[index],
                "红球和值": sum(self.red_balls[index]),
                "蓝球和值": sum(self.blue_balls[index]),
                "总和值": sum(self.red_balls[index]) + sum(self.blue_balls[index])
            }
        except ValueError:
            return {"error": f"期号 {period} 不存在于数据中"}

    def get_recent_draws(self, count=10):
        """
        获取最近N期的开奖数据

        Args:
            count: 获取期数，默认10期

        Returns:
            dict: 包含数据列表或错误信息的字典
        """
        if not self.periods:
            return {"error": "数据未加载，请先调用 load_data()"}

        # 参数验证
        if not isinstance(count, int) or count <= 0:
            return {"error": "count参数必须是正整数"}

        actual_count = min(count, len(self.periods))

        recent_data = []
        for i in range(actual_count):
            recent_data.append({
                "期号": self.periods[i],
                "开奖日期": self.dates[i],
                "红球": self.red_balls[i],
                "蓝球": self.blue_balls[i],
                "红球和值": sum(self.red_balls[i]),
                "蓝球和值": sum(self.blue_balls[i])
            })

        return {
            "请求期数": count,
            "实际期数": actual_count,
            "数据": recent_data
        }

    def validate_number_combination(self, red_numbers, blue_numbers):
        """
        验证号码组合的有效性

        Args:
            red_numbers: 红球号码列表（5个数字）
            blue_numbers: 蓝球号码列表（2个数字）

        Returns:
            dict: 验证结果
        """
        errors = []

        # 验证红球
        if not isinstance(red_numbers, (list, tuple)) or len(red_numbers) != 5:
            errors.append("红球必须是包含5个数字的列表")
        else:
            # 更严格的类型检查，支持int和float类型
            try:
                red_ints = [int(n) for n in red_numbers]
                if not all(1 <= n <= 35 for n in red_ints):
                    errors.append("红球号码必须是1-35之间的整数")
                if len(set(red_ints)) != 5:
                    errors.append("红球号码不能重复")
                red_numbers = red_ints  # 转换为整数列表
            except (ValueError, TypeError):
                errors.append("红球号码必须是可转换为整数的数值")

        # 验证蓝球
        if not isinstance(blue_numbers, (list, tuple)) or len(blue_numbers) != 2:
            errors.append("蓝球必须是包含2个数字的列表")
        else:
            # 更严格的类型检查，支持int和float类型
            try:
                blue_ints = [int(n) for n in blue_numbers]
                if not all(1 <= n <= 12 for n in blue_ints):
                    errors.append("蓝球号码必须是1-12之间的整数")
                if len(set(blue_ints)) != 2:
                    errors.append("蓝球号码不能重复")
                blue_numbers = blue_ints  # 转换为整数列表
            except (ValueError, TypeError):
                errors.append("蓝球号码必须是可转换为整数的数值")

        if errors:
            return {"valid": False, "errors": errors}

        return {
            "valid": True,
            "红球": red_numbers,  # 已经是列表，无需转换
            "蓝球": blue_numbers,  # 已经是列表，无需转换
            "红球和值": sum(red_numbers),
            "蓝球和值": sum(blue_numbers),
            "总和值": sum(red_numbers) + sum(blue_numbers)
        }

    def check_historical_occurrence(self, red_numbers, blue_numbers):
        """
        检查指定号码组合是否在历史中出现过

        Args:
            red_numbers: 红球号码列表
            blue_numbers: 蓝球号码列表

        Returns:
            dict: 检查结果
        """
        if not self.red_balls or len(self.red_balls) == 0:
            return {"error": "数据未加载或为空，请先调用 load_data()"}

        # 先验证号码组合
        validation = self.validate_number_combination(red_numbers, blue_numbers)
        if not validation["valid"]:
            return validation

        red_set = set(sorted(red_numbers))
        blue_set = set(sorted(blue_numbers))

        # 检查各种匹配情况
        exact_matches = []
        red_matches = []
        blue_matches = []
        partial_matches = []

        for i, (red_draw, blue_draw) in enumerate(zip(self.red_balls, self.blue_balls)):
            red_draw_set = set(red_draw)
            blue_draw_set = set(blue_draw)

            # 计算红球和蓝球的匹配个数
            red_match_count = len(red_set & red_draw_set)
            blue_match_count = len(blue_set & blue_draw_set)

            # 完全匹配（红球和蓝球都完全匹配）
            if red_set == red_draw_set and blue_set == blue_draw_set:
                exact_matches.append({
                    "期号": self.periods[i],
                    "开奖日期": self.dates[i],
                    "红球匹配数": 5,
                    "蓝球匹配数": 2
                })

            # 红球完全匹配
            elif red_set == red_draw_set:
                red_matches.append({
                    "期号": self.periods[i],
                    "开奖日期": self.dates[i],
                    "蓝球": blue_draw,
                    "红球匹配数": 5,
                    "蓝球匹配数": blue_match_count
                })

            # 蓝球完全匹配
            elif blue_set == blue_draw_set:
                blue_matches.append({
                    "期号": self.periods[i],
                    "开奖日期": self.dates[i],
                    "红球": red_draw,
                    "红球匹配数": red_match_count,
                    "蓝球匹配数": 2
                })

            # 部分匹配（更严格的条件：红球匹配3个以上且蓝球至少匹配1个，或红球匹配4个以上）
            elif (red_match_count >= 3 and blue_match_count >= 1) or red_match_count >= 4:
                partial_matches.append({
                    "期号": self.periods[i],
                    "开奖日期": self.dates[i],
                    "红球": red_draw,
                    "蓝球": blue_draw,
                    "红球匹配数": red_match_count,
                    "蓝球匹配数": blue_match_count
                })

        return {
            "查询组合": {
                "红球": sorted(red_numbers),
                "蓝球": sorted(blue_numbers)
            },
            "完全匹配": exact_matches,
            "红球完全匹配": red_matches,
            "蓝球完全匹配": blue_matches,
            "部分匹配": partial_matches,
            "匹配统计": {
                "完全匹配次数": len(exact_matches),
                "红球完全匹配次数": len(red_matches),
                "蓝球完全匹配次数": len(blue_matches),
                "部分匹配次数": len(partial_matches),
                "总匹配次数": len(exact_matches) + len(red_matches) + len(blue_matches) + len(partial_matches)
            },
            "匹配详情": exact_matches + red_matches + blue_matches + partial_matches
        }

    def _calculate_skewness(self, data):
        """
        计算偏度（skewness）

        Args:
            data: numpy数组

        Returns:
            float: 偏度值
        """
        if len(data) < 3:
            return 0.0

        mean = np.mean(data)
        std = np.std(data, ddof=1)  # 使用样本标准差保持一致性

        if std == 0:
            return 0.0

        # 计算三阶中心矩
        skewness = np.mean(((data - mean) / std) ** 3)
        return skewness

    def _calculate_kurtosis(self, data):
        """
        计算峰度（kurtosis）

        Args:
            data: numpy数组

        Returns:
            float: 峰度值（超额峰度，正态分布为0）
        """
        if len(data) < 4:
            return 0.0

        mean = np.mean(data)
        std = np.std(data, ddof=1)  # 使用样本标准差保持一致性

        if std == 0:
            return 0.0

        # 计算四阶中心矩
        kurtosis = np.mean(((data - mean) / std) ** 4) - 3  # 减3得到超额峰度
        return kurtosis

    def _chi_square_test(self, observed, expected):
        """
        计算卡方检验统计量和p值

        Args:
            observed: 观察频率列表
            expected: 期望频率列表

        Returns:
            tuple: (卡方统计量, p值)
        """
        observed = np.array(observed, dtype=np.float64)
        expected = np.array(expected, dtype=np.float64)

        # 避免除零错误
        expected = np.where(expected == 0, 1e-10, expected)

        # 计算卡方统计量
        chi2_stat = np.sum((observed - expected) ** 2 / expected)

        # 计算自由度
        df = len(observed) - 1

        # 简化的p值计算（使用近似公式）
        # 对于大样本，卡方分布近似正态分布
        if df > 30:
            # 使用正态近似（卡方检验是单尾检验）
            mean = df
            variance = 2 * df
            z_score = (chi2_stat - mean) / np.sqrt(variance)
            # 修正：卡方检验是右尾单尾检验
            p_value = 1 - self._normal_cdf(z_score)
        else:
            # 使用简化的卡方分布表查找
            p_value = self._chi2_p_value_approx(chi2_stat, df)

        return chi2_stat, max(0.0, min(1.0, p_value))

    def _normal_cdf(self, x):
        """
        标准正态分布的累积分布函数近似（使用更精确的近似公式）

        Args:
            x: 标准化值

        Returns:
            float: 累积概率
        """
        # 使用Abramowitz and Stegun近似公式，精度更高
        if x < 0:
            return 1 - self._normal_cdf(-x)

        # 常数
        a1 = 0.254829592
        a2 = -0.284496736
        a3 = 1.421413741
        a4 = -1.453152027
        a5 = 1.061405429
        p = 0.3275911

        # 计算
        t = 1.0 / (1.0 + p * x)
        y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * np.exp(-x * x / 2.0) / np.sqrt(2 * np.pi)

        return y

    def _chi2_p_value_approx(self, chi2_stat, df):
        """
        卡方分布p值的改进近似计算
        使用Wilson-Hilferty变换进行更准确的近似

        Args:
            chi2_stat: 卡方统计量
            df: 自由度

        Returns:
            float: p值近似值
        """
        if df <= 0:
            return 1.0

        # Wilson-Hilferty变换：将卡方分布转换为近似正态分布
        # 这是一个更准确的近似方法
        h = 2.0 / (9.0 * df)
        z = (np.power(chi2_stat / df, 1.0/3.0) - (1.0 - h)) / np.sqrt(h)

        # 使用标准正态分布的右尾概率
        p_value = 1.0 - self._normal_cdf(z)

        # 确保p值在合理范围内
        return max(0.0001, min(0.9999, p_value))


def main():
    """示例用法"""
    print("大乐透基础分析库示例")
    print("=" * 50)

    # 创建分析器实例
    analyzer = LotteryAnalyzer()

    # 加载数据
    if not analyzer.load_data():
        print("数据加载失败")
        return

    print("\n数据概览:")
    summary = analyzer.get_data_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")

    print("\n红球频率分析（前10个）:")
    red_freq = analyzer.analyze_frequency("red")
    if "频率统计" in red_freq:
        sorted_freq = sorted(red_freq["频率统计"].items(),
                           key=lambda x: x[1]["出现次数"], reverse=True)
        for i, (num, info) in enumerate(sorted_freq[:10]):
            print(f"  {num}号: {info['出现次数']}次 (频率: {info['出现频率']})")

    print("\n红球冷热号分析:")
    hot_cold = analyzer.analyze_hot_cold("red", 50)
    if "热号" in hot_cold:
        print(f"  热号: {[item['号码'] for item in hot_cold['热号'][:5]]}")
        print(f"  冷号: {[item['号码'] for item in hot_cold['冷号'][:5]]}")

    print("\n最近5期开奖:")
    recent = analyzer.get_recent_draws(5)
    if isinstance(recent, list):
        for draw in recent:
            print(f"  {draw['期号']}: 红球{draw['红球']} 蓝球{draw['蓝球']}")


if __name__ == "__main__":
    main()
